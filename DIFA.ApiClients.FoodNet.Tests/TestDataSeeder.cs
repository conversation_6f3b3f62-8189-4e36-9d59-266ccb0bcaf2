using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Entities;
using Newtonsoft.Json;
using System.Reflection;

namespace DIFA.ApiClients.FoodNet.Tests;

public class TestDataSeeder
{
    private readonly TestDataModel _testData;

    public TestDataSeeder()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var resourceName = "DIFA.ApiClients.FoodNet.Tests.TestData.Entities.json";
        
        using var stream = assembly.GetManifestResourceStream(resourceName)
                          ?? throw new InvalidOperationException($"Could not find embedded resource: {resourceName}");
        using var reader = new StreamReader(stream);
        var json = reader.ReadToEnd();
        
        _testData = JsonConvert.DeserializeObject<TestDataModel>(json) 
                   ?? throw new InvalidOperationException("Failed to load test data");
    }

    public void SeedAll(AlphaContext alphaContext, FoodNetContext foodNetContext, DynamoContext dynamoContext)
    {
        SeedAlphaContext(alphaContext);
        SeedFoodNetContext(foodNetContext);
        SeedDynamoContext(dynamoContext);
    }

    private void SeedAlphaContext(AlphaContext context)
    {
        // LCEs
        var lces = _testData.LCEs.Select(data => new LCE
        {
            ServiceCode = data.ServiceCode,
            DescriptionNl = data.DescriptionNl,
            DescriptionFr = data.DescriptionFr
        });
        context.LCEs.AddRange(lces);

        // PCEs
        var pces = _testData.PCEs.Select(data => new PCE
        {
            ServiceCode = data.ServiceCode,
            DescriptionNl = data.DescriptionNl,
            DescriptionFr = data.DescriptionFr
        });
        context.PCEs.AddRange(pces);

        // Parameters
        var parameters = _testData.Parameters.Select(data => new Parameter
        {
            Id = data.Id,
            DescriptionNl = data.DescriptionNl,
            DescriptionFr = data.DescriptionFr
        });
        context.Parameters.AddRange(parameters);

        // Matrix Level 4
        var matrix4s = _testData.MatrixLevel4.Select(data => new MatrixLevel4
        {
            MatrixNiv4Id = data.MatrixNiv4Id,
            DescriptionNl = data.DescriptionNl,
            DescriptionFr = data.DescriptionFr
        });
        context.MatrixLevel4s.AddRange(matrix4s);

        // Matrix Level 5
        var matrix5s = _testData.MatrixLevel5.Select(data => new MatrixLevel5
        {
            MatrixNiv5Id = data.MatrixNiv5Id,
            DescriptionNl = data.DescriptionNl,
            DescriptionFr = data.DescriptionFr
        });
        context.MatrixLevel5s.AddRange(matrix5s);

        context.SaveChanges();
    }

    private void SeedFoodNetContext(FoodNetContext context)
    {
        // Missions
        var missions = _testData.Missions.Select(data => new Mission
        {
            MissionId = data.MissionId,
            MissionNb = data.MissionNb,
            OperatorId = data.OperatorId,
            PceCodeOperator = data.PceCodeOperator,
            Noe = data.Noe,
            PlannedStartDateHour = data.PlannedStartDateHour,
            PlannedEndDateHour = data.PlannedEndDateHour,
            ActualStartDateHour = data.ActualStartDateHour,
            ActualEndDateHour = data.ActualEndDateHour
        });
        context.Missions.AddRange(missions);

        // Samples
        var samples = _testData.Samples.Select(data => new Sample
        {
            SampleId = data.SampleId,
            SampleParameterId = data.SampleParameterId,
            SampleNb = data.SampleNb,
            ControlId = data.ControlId,
            SamplingId = data.SamplingId,
            ParameterId = data.ParameterId,
            ParameterResult = data.ParameterResult,
            SampleResult = data.SampleResult,
            IsExtra = data.IsExtra,
            MissionId = data.MissionId
        });
        context.Samples.AddRange(samples);

        // Answers
        var answers = _testData.Answers.Select(data => new Answer
        {
            Id = data.Id,
            Result = data.Result,
            ScoreId = data.ScoreId
        });
        context.Answers.AddRange(answers);

        context.SaveChanges();
    }

    private void SeedDynamoContext(DynamoContext context)
    {
        // For now, just save - Questions are handled via raw SQL
        context.SaveChanges();
    }
}

// Data models for JSON deserialization
public class TestDataModel
{
    public List<LCEData> LCEs { get; set; } = new();
    public List<PCEData> PCEs { get; set; } = new();
    public List<ParameterData> Parameters { get; set; } = new();
    public List<MatrixLevel4Data> MatrixLevel4 { get; set; } = new();
    public List<MatrixLevel5Data> MatrixLevel5 { get; set; } = new();
    public List<MissionData> Missions { get; set; } = new();
    public List<SampleData> Samples { get; set; } = new();
    public List<AnswerData> Answers { get; set; } = new();
}

public class LCEData
{
    public string ServiceCode { get; set; } = string.Empty;
    public string DescriptionNl { get; set; } = string.Empty;
    public string DescriptionFr { get; set; } = string.Empty;
}

public class PCEData
{
    public string ServiceCode { get; set; } = string.Empty;
    public string DescriptionNl { get; set; } = string.Empty;
    public string DescriptionFr { get; set; } = string.Empty;
}

public class ParameterData
{
    public int Id { get; set; }
    public string DescriptionNl { get; set; } = string.Empty;
    public string DescriptionFr { get; set; } = string.Empty;
}

public class MatrixLevel4Data
{
    public string MatrixNiv4Id { get; set; } = string.Empty;
    public string DescriptionNl { get; set; } = string.Empty;
    public string DescriptionFr { get; set; } = string.Empty;
}

public class MatrixLevel5Data
{
    public string MatrixNiv5Id { get; set; } = string.Empty;
    public string DescriptionNl { get; set; } = string.Empty;
    public string DescriptionFr { get; set; } = string.Empty;
}

public class MissionData
{
    public long MissionId { get; set; }
    public string MissionNb { get; set; } = string.Empty;
    public int OperatorId { get; set; }
    public string PceCodeOperator { get; set; } = string.Empty;
    public int Noe { get; set; }
    public DateTime PlannedStartDateHour { get; set; }
    public DateTime PlannedEndDateHour { get; set; }
    public DateTime ActualStartDateHour { get; set; }
    public DateTime ActualEndDateHour { get; set; }
}

public class SampleData
{
    public string SampleId { get; set; } = string.Empty;
    public string SampleParameterId { get; set; } = string.Empty;
    public string SampleNb { get; set; } = string.Empty;
    public string ControlId { get; set; } = string.Empty;
    public string SamplingId { get; set; } = string.Empty;
    public int ParameterId { get; set; }
    public string ParameterResult { get; set; } = string.Empty;
    public string SampleResult { get; set; } = string.Empty;
    public bool IsExtra { get; set; }
    public long MissionId { get; set; }
}

public class AnswerData
{
    public int Id { get; set; }
    public string Result { get; set; } = string.Empty;
    public int ScoreId { get; set; }
}
