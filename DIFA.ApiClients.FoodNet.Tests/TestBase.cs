using DIFA.ApiClients.FoodNet.Mock;
using DIFA.ApiClients.FoodNet.Services;
using Moq;
using WireMock.Server;

namespace DIFA.ApiClients.FoodNet.Tests;

/// <summary>
/// Base class for all test classes to reduce code duplication
/// </summary>
public abstract class TestBase : IDisposable
{
    private readonly WireMockServer _mockServer;
    private readonly HttpClient _httpClient;

    // FoodNet Client for testing
    protected readonly IFoodNetClient Client;

    protected TestBase()
    {
        // Setup WireMock server
        _mockServer = WireMockServer.Start();
        _mockServer.AllowPartialMapping();
        _mockServer.WithMapping(FoodNetMockData.AllMappings);

        // Configure HttpClient to use mock server
        _httpClient = new HttpClient
        {
            BaseAddress = new Uri(_mockServer.Urls[0] + "/FoodNet.WebAPI/")
        };
        
        Mock<Mission> mockMissionService = new();
        Mock<LceService> mockLCEService = new();
        Mock<MatrixService> mockMatrixService = new();
        Mock<ParameterService> mockParameterService = new();
        Mock<SampleService> mockSampleService = new();
        Mock<CheckListService> mockCheckListService = new();
        Mock<AnswerService> mockAnswerService = new();

        // Create FoodNetClient with mocked services
        Client = new FoodNetClient(
            mockLCEService.Object,
            mockMatrixService.Object,
            mockParameterService.Object,
            mockCheckListService.Object,
            mockMissionService.Object,
            mockSampleService.Object,
            mockAnswerService.Object);
    }
    
    public void Dispose()
    {
        _mockServer.Stop();
        _httpClient.Dispose();
        GC.SuppressFinalize(this);
    }
}
