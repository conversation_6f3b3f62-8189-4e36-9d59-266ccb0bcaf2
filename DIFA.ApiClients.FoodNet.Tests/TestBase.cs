using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Services;
using Microsoft.EntityFrameworkCore;
using AutoMapper;

namespace DIFA.ApiClients.FoodNet.Tests;

/// <summary>
/// Base class for all test classes to reduce code duplication
/// </summary>
public abstract class TestBase : IDisposable
{
    protected readonly FoodNetContext FoodNetContext;
    protected readonly AlphaContext AlphaContext;
    protected readonly DynamoContext DynamoContext;
    protected readonly IMapper Mapper;
    
    // Real services for testing
    protected readonly IMissionService MissionService;
    protected readonly ILCEService LceService;
    protected readonly IMatrixService MatrixService;
    protected readonly IParameterService ParameterService;
    protected readonly ISampleService SampleService;
    protected readonly ICheckListService CheckListService;
    protected readonly IAnswerService AnswerService;

    // FoodNet Client for testing
    protected readonly IFoodNetClient Client;
    
    private readonly TestDataSeeder _seeder;

    protected TestBase()
    {
        // Setup in-memory databases
        FoodNetContext = new FoodNetContext(new DbContextOptionsBuilder<FoodNetContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options);
            
        AlphaContext = new AlphaContext(new DbContextOptionsBuilder<AlphaContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options);
            
        DynamoContext = new DynamoContext(new DbContextOptionsBuilder<DynamoContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options);
        
        // Seed test data
        _seeder = new TestDataSeeder();
        _seeder.SeedAll(AlphaContext, FoodNetContext, DynamoContext);

        // Create real services
        MissionService = new MissionService(FoodNetContext, AlphaContext, Mapper);
        LceService = new LceService(AlphaContext);
        MatrixService = new MatrixService(AlphaContext);
        ParameterService = new ParameterService(AlphaContext);
        SampleService = new SampleService(FoodNetContext, ParameterService, MatrixService);
        CheckListService = new CheckListService(DynamoContext, FoodNetContext);
        AnswerService = new AnswerService(FoodNetContext);

        // Create FoodNetClient
        Client = new FoodNetClient(
            LceService,
            MatrixService,
            ParameterService,
            CheckListService,
            MissionService,
            SampleService,
            AnswerService);
    }
    
    public void Dispose()
    {
        FoodNetContext?.Dispose();
        AlphaContext?.Dispose();
        DynamoContext?.Dispose();
        GC.SuppressFinalize(this);
    }
}