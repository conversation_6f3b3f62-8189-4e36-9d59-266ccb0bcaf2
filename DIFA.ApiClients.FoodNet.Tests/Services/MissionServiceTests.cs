using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

[Trait("Category", "Unit")]
public class MissionServiceTests : TestBase
{
    [Fact]
    public async Task GetMissionsAsync_WithValidOperatorId_ReturnsFilteredMissions()
    {
        // Arrange
        var operatorId = 2087535;

        // Act - Using FoodNetClient which delegates to WireMock
        var result = await Client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2); // Based on the JSON mock data
        
        var missions = result.ToList();
        var firstMission = missions.First(m => m.MissionId == 1673);
        firstMission.MissionNb.Should().Be("y5z6a7b8-c9d0-1234-yzab-567890123456");
        firstMission.LCE.Should().Be("i9j0k1l2-m3n4-5678-ijkl-901234567890");
        firstMission.LceDto.Should().NotBeNull();
        firstMission.LceDto!.ServiceCode.Should().Be("i9j0k1l2-m3n4-5678-ijkl-901234567890");
        firstMission.LceDto.DescriptionNl.Should().Be("Laboratorium Controle Eenheid 1 NL");
        
        var secondMission = missions.First(m => m.MissionId == 1674);
        secondMission.MissionNb.Should().Be("z6a7b8c9-d0e1-2345-zabc-678901234567");
        secondMission.LCE.Should().Be("j0k1l2m3-n4o5-6789-jklm-012345678901");
        secondMission.LceDto.Should().NotBeNull();
        secondMission.LceDto!.ServiceCode.Should().Be("j0k1l2m3-n4o5-6789-jklm-012345678901");
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithValidMissionId_ReturnsMission()
    {
        // Arrange
        var missionId = "1673";

        // Act - Using FoodNetClient which delegates to WireMock
        var result = await Client.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().NotBeNull();
        result!.MissionId.Should().Be(1673);
        result.MissionNb.Should().Be("y5z6a7b8-c9d0-1234-yzab-567890123456");
        result.LCE.Should().Be("i9j0k1l2-m3n4-5678-ijkl-901234567890");
        result.LceDto.Should().NotBeNull();
        result.LceDto!.ServiceCode.Should().Be("i9j0k1l2-m3n4-5678-ijkl-901234567890");
        result.LceDto.DescriptionNl.Should().Be("Laboratorium Controle Eenheid 1 NL");
    }

    [Fact]
    public async Task GetMissionByIdAsync_WithNonExistentMissionId_ReturnsNull()
    {
        // Arrange
        var missionId = "9999"; // This ID should trigger the mission-not-found.json mapping

        // Act
        var result = await Client.GetMissionByIdAsync(missionId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetMissionsAsync_WithNoMatchingMissions_ReturnsEmptyList()
    {
        // Arrange
        var operatorId = 9999; // This should trigger the missions-empty-operator.json mapping

        // Act
        var result = await Client.GetMissionsAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }
}
