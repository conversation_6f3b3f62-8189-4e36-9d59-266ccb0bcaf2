using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

public class ParameterServiceTests : TestBase
{
    [Fact]
    public async Task GetParametersAsync_WithParameters_ReturnsAllParameters()
    {
        // Act
        var result = await Client.GetParametersAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(5); 
        
        var salmonellaParameter = result.First(p => p.Id == 1);
        salmonellaParameter.DescriptionNL.Should().Be("Salmonella spp.");
        salmonellaParameter.DescriptionFR.Should().Be("Salmonella spp.");
        
        var listeriaParameter = result.First(p => p.Id == 2);
        listeriaParameter.DescriptionNL.Should().Be("Listeria monocytogenes");
        listeriaParameter.DescriptionFR.Should().Be("Listeria monocytogenes");
        
        var ecoliParameter = result.First(p => p.Id == 3);
        ecoliParameter.DescriptionNL.Should().Be("Escherichia coli");
        ecoliParameter.DescriptionFR.Should().Be("Escherichia coli");
    }

    [Fact]
    public async Task GetParameterByIdAsync_WithValidId_ReturnsParameter()
    {
        // Act
        var result = await Client.GetParameterByIdAsync(1, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(1);
        result.DescriptionNL.Should().Be("Salmonella spp.");
        result.DescriptionFR.Should().Be("Salmonella spp.");
    }

    [Fact]
    public async Task GetParameterByIdAsync_WithNonExistentId_ReturnsNull()
    {
        // Act
        var result = await Client.GetParameterByIdAsync(9999, CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }
}
