using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

public class LCETests : TestBase
{
    [Fact]
    public async Task GetLCEs_WithLCEAndPCEData_ReturnsUnionOfBoth()
    {
        // Act
        var result = await Client.GetLCEsAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(4);
        
        result.Should().Contain(lce => lce.ServiceCode == "LCE001");
        result.Should().Contain(lce => lce.ServiceCode == "LCE002");
        result.Should().Contain(lce => lce.ServiceCode == "PCE001");
        result.Should().Contain(lce => lce.ServiceCode == "PCE002");
        
        // Verify descriptions
        var lce001 = result.First(lce => lce.ServiceCode == "LCE001");
        lce001.DescriptionNl.Should().Be("Laboratorium Controle Eenheid 1 NL");
        lce001.DescriptionFr.Should().Be("Unité de Contrôle Laboratoire 1 FR");
    }

    [Fact]
    public async Task GetLCEByServiceCodeAsync_WithValidServiceCode_ReturnsLCE()
    {
        // Act
        var result = await Client.GetLCEByServiceCodeAsync("LCE001", CancellationToken.None);
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetLCEByServiceCodeAsync_WithNonExistentServiceCode_ReturnsNull()
    {
        // Act
        var result = await Client.GetLCEByServiceCodeAsync("NON_EXISTENT", CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }
}
