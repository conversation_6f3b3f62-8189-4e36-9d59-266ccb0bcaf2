using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

public class SampleServiceTests : TestBase
{
    [Fact]
    public async Task GetSamplesAsync_WithValidOperatorId_ReturnsFilteredSamples()
    {
        // Arrange
        var operatorId = 2087535;

        // Act
        var result = await Client.GetSamplesAsync(operatorId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var samples = result.ToList();
        var firstSample = samples.First(s => s.SampleNumber == "SAMPLE001");
        firstSample.ControlId.Should().Be("CTRL001");
        firstSample.Result.Should().Be("CONFORM");
        firstSample.MatrixNiv4Id.Should().Be("M4_001");
        firstSample.MatrixNiv5Id.Should().Be("M5_001");
        firstSample.MatrixNiv4.Should().NotBeNull();
        firstSample.MatrixNiv4!.MatrixId.Should().Be("M4_001");
        firstSample.MatrixNiv5.Should().NotBeNull();
        firstSample.MatrixNiv5!.MatrixId.Should().Be("M5_001");
        
        var secondSample = samples.First(s => s.SampleNumber == "SAMPLE002");
        secondSample.ControlId.Should().Be("CTRL002");
        secondSample.Result.Should().Be("NON-CONFORM");
    }

    [Fact]
    public async Task GetSampleDetailsAsync_WithValidParameters_ReturnsSampleDetails()
    {
        // Arrange
        var operatorId = 2087535;
        var controlId = 12345;

        // Act
        var result = await Client.GetSampleDetailsAsync(operatorId, controlId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        
        var samples = result.ToList();
        var firstSample = samples.First(s => s.SampleId == "SAMPLE001");
        firstSample.ParameterId.Should().Be(1);
        firstSample.Parameter.Should().NotBeNull();
        firstSample.Parameter!.DescriptionNL.Should().Be("Salmonella spp.");
        firstSample.IsExtra.Should().BeFalse();
        firstSample.SampleResult.Should().Be("CONFORM");
        
        var secondSample = samples.First(s => s.SampleId == "SAMPLE002");
        secondSample.ParameterId.Should().Be(2);
        secondSample.Parameter.Should().NotBeNull();
        secondSample.Parameter!.DescriptionNL.Should().Be("Listeria monocytogenes");
        secondSample.IsExtra.Should().BeTrue();
        secondSample.SampleResult.Should().Be("NON-CONFORM");
    }

    [Fact]
    public async Task GetSampleByIdAsync_WithValidSampleId_ReturnsSample()
    {
        // Arrange
        var sampleId = "SAMPLE001";

        // Act
        var result = await Client.GetSampleByIdAsync(sampleId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetSampleByIdAsync_WithNonExistentSampleId_ReturnsNull()
    {
        // Act
        var result = await Client.GetSampleByIdAsync("NON_EXISTENT");

        // Assert
        result.Should().BeNull();
    }
}
