{"FoodNet": {"FoodNetConnectionString": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=hypnos.linux.ici)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=FOODTE1)));User Id=foodnet_consult;Password=*************;", "AlphaConnectionString": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=hypnos.linux.ici)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=FOODTE1)));User Id=alpha_consult;Password=alpha_consult;", "DynamoConnectionString": "Server=VM-MSSQLTESTE.teste.favv-afsca.be\\MSSQLTESTE;Database=DynamoTeste;Encrypt=false;User Id=Dynamo_Consult;Password=*************;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}