using Azure.Identity;
using MartinCostello.Logging.XUnit;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit.Abstractions;

namespace DIFA.ApiClients.FoodNet.Tests.IntegrationTestSetup;

public class InjectionFixture : ITestOutputHelperAccessor
{
    public ITestOutputHelper? OutputHelper { get; set; }

    /// <summary>
    /// A fixture for injecting the necessary services for integration tests
    /// </summary>
    /// <remarks>
    /// The fixture creates a <see cref="IServiceProvider"/> that has been configured with
    /// the minimal services required to run the integration tests. This includes the
    /// <see cref="IDistributedCache"/> and FoodNet services.
    /// <para>
    /// The configuration is loaded from the "IntegrationTestSetup/appsettings.json" file
    /// and the Azure Key Vault. The environment variables are also included in the
    /// configuration.
    /// </para>
    /// </remarks>
    public InjectionFixture()
    {
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("IntegrationTestSetup/appsettings.json", optional: false, reloadOnChange: true)
            .AddAzureKeyVault(new Uri("https://kvconsumerdevgwc001.vault.azure.net/"), new DefaultAzureCredential())
            .AddEnvironmentVariables()
            .Build();

        var foodNetConfiguration = configuration.GetSection("FoodNet")
            .Get<FoodNetConfiguration>();

        ServiceProvider = new ServiceCollection()
            .AddLogging(builder => builder.AddXUnit(this))
            .AddDistributedMemoryCache()
            .AddFoodNetClient(
                foodNetConfiguration!.FoodNetConnectionString,
                foodNetConfiguration.AlphaConnectionString,
                foodNetConfiguration.DynamoConnectionString)
            .BuildServiceProvider();
    }

    public IServiceProvider ServiceProvider { get; }
}
