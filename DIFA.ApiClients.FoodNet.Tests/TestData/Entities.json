{"lces": [{"serviceCode": "LCE001", "descriptionNl": "Laboratorium Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Laboratoire 1 FR"}, {"serviceCode": "LCE002", "descriptionNl": "Laboratorium Controle Eenheid 2 NL", "descriptionFr": "Unité de Contrôle Laboratoire 2 FR"}], "pces": [{"serviceCode": "PCE001", "descriptionNl": "Post Controle Eenheid 1 NL", "descriptionFr": "Unité de Contrôle Post 1 FR"}, {"serviceCode": "PCE002", "descriptionNl": "Post Controle Eenheid 2 NL", "descriptionFr": "Unité de Contrôle Post 2 FR"}], "parameters": [{"id": 1, "descriptionNl": "Salmonella spp.", "descriptionFr": "Salmonella spp."}, {"id": 2, "descriptionNl": "Listeria monocytogenes", "descriptionFr": "Listeria monocytogenes"}, {"id": 3, "descriptionNl": "Escherichia coli", "descriptionFr": "Escherichia coli"}, {"id": 4, "descriptionNl": "Campylobacter spp.", "descriptionFr": "Campylobacter spp."}, {"id": 5, "descriptionNl": "Staphylococcus aureus", "descriptionFr": "Staphylococcus aureus"}], "matrixLevel4": [{"matrixNiv4Id": "M4_001", "descriptionNl": "Matrix Level 4 Test 1 NL", "descriptionFr": "Matrix Level 4 Test 1 FR"}, {"matrixNiv4Id": "M4_002", "descriptionNl": "Matrix Level 4 Test 2 NL", "descriptionFr": "Matrix Level 4 Test 2 FR"}, {"matrixNiv4Id": "M4_003", "descriptionNl": "Vlees en vleesproducten", "descriptionFr": "Viande et produits carnés"}, {"matrixNiv4Id": "M4_004", "descriptionNl": "Zuivelproducten", "descriptionFr": "Produits laitiers"}], "matrixLevel5": [{"matrixNiv5Id": "M5_001", "descriptionNl": "Matrix Level 5 Test 1 NL", "descriptionFr": "Matrix Level 5 Test 1 FR"}, {"matrixNiv5Id": "M5_002", "descriptionNl": "Matrix Level 5 Test 2 NL", "descriptionFr": "Matrix Level 5 Test 2 FR"}, {"matrixNiv5Id": "M5_003", "descriptionNl": "Rundvlees vers", "descriptionFr": "Bœuf frais"}, {"matrixNiv5Id": "M5_004", "descriptionNl": "Melk ruw", "descriptionFr": "Lait cru"}], "missions": [{"missionId": 1673, "missionNb": "y5z6a7b8-c9d0-1234-yzab-567890123456", "operatorId": 2087535, "pceCodeOperator": "LCE001", "noe": 0, "plannedStartDateHour": "2024-01-14T08:00:00Z", "plannedEndDateHour": "2024-01-14T17:00:00Z", "actualStartDateHour": "2024-01-14T08:15:00Z", "actualEndDateHour": "2024-01-14T16:45:00Z"}, {"missionId": 1674, "missionNb": "z6a7b8c9-d0e1-2345-zabc-678901234567", "operatorId": 2087535, "pceCodeOperator": "LCE002", "noe": 0, "plannedStartDateHour": "2024-01-13T09:00:00Z", "plannedEndDateHour": "2024-01-13T18:00:00Z", "actualStartDateHour": "2024-01-13T09:30:00Z", "actualEndDateHour": "2024-01-13T17:30:00Z"}], "samples": [{"sampleId": "q7r8s9t0-u1v2-3456-qrst-789012345678", "sampleParameterId": "r8s9t0u1-v2w3-4567-rstu-890123456789", "sampleNb": "s9t0u1v2-w3x4-5678-stuv-901234567890", "controlId": "12345", "samplingId": "t0u1v2w3-x4y5-6789-tuvw-012345678901", "parameterId": 1, "parameterResult": "< 10 CFU/g", "sampleResult": "CONFORM", "isExtra": false, "missionId": 1673}, {"sampleId": "u1v2w3x4-y5z6-7890-uvwx-123456789012", "sampleParameterId": "v2w3x4y5-z6a7-8901-vwxy-234567890123", "sampleNb": "w3x4y5z6-a7b8-9012-wxyz-345678901234", "controlId": "12345", "samplingId": "x4y5z6a7-b8c9-0123-xyza-456789012345", "parameterId": 2, "parameterResult": "> 100 CFU/g", "sampleResult": "NON-CONFORM", "isExtra": true, "missionId": 1673}], "answers": [{"id": 1, "result": "<PERSON>a", "scoreId": 1}, {"id": 2, "result": "<PERSON><PERSON>", "scoreId": 2}, {"id": 3, "result": "<PERSON>a", "scoreId": 1}, {"id": 4, "result": "4.2°C", "scoreId": 1}]}