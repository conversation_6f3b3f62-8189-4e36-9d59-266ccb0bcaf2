﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Moq" Version="4.20.72" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.17" />
        <PackageReference Include="AutoMapper" Version="14.0.0" />
        <PackageReference Include="FluentAssertions" Version="8.4.0" />
        <PackageReference Include="Azure.Identity" Version="1.14.1" />
        <PackageReference Include="MartinCostello.Logging.XUnit" Version="0.6.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.24" />
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet\DIFA.ApiClients.FoodNet.csproj" />
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet.Contract\DIFA.ApiClients.FoodNet.Contract.csproj" />
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet.Entities\DIFA.ApiClients.FoodNet.Entities.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Include="IntegrationTestSetup\appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
