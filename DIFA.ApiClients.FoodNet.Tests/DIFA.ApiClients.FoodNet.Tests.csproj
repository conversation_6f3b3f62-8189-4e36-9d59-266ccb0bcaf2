<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Identity" Version="1.13.2"/>
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="FluentAssertions" Version="[7.2.0]" />
        <PackageReference Include="MartinCostello.Logging.XUnit" Version="0.5.1"/>
        <PackageReference Include="Microsoft.AspNetCore.TestHost" Version="8.0.14" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.17" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
        <PackageReference Include="Moq" Version="4.20.72"/>
        <PackageReference Include="WireMock.Net" Version="1.7.4" />
        <PackageReference Include="xunit" Version="2.9.3"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet\DIFA.ApiClients.FoodNet.csproj" />
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet.Contract\DIFA.ApiClients.FoodNet.Contract.csproj" />
        <ProjectReference Include="..\DIFA.ApiClients.FoodNet.Entities\DIFA.ApiClients.FoodNet.Entities.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Include="IntegrationTestSetup\appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="TestData\Entities.json" />
    </ItemGroup>

</Project>
