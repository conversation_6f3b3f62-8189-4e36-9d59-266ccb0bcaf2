namespace DIFA.ApiClients.FoodNet.Entities;

public class Question
{
    public int ItemId { get; init; }
    public string? ParentChapterTitle { get; init; } // Can be NULL from LEFT JOIN
    public string? ParentTitle { get; init; } // Can be NULL
    public string? Title { get; init; } // Can be NULL
    public string? QuestionType { get; init; } // Can be NULL
    public int QuestionTypeId { get; init; }
    public int TemplateVersionId { get; init; }
}