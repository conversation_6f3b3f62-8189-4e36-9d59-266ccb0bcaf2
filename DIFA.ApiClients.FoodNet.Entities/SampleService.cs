namespace DIFA.ApiClients.FoodNet.Entities;

public class SampleService
{
    public required string SampleId { get; init; }
    public required string SampleParameterId { get; init; } 
    public required string SampleNb { get; init; } 
    public required string ControlId { get; init; }
    public required string SamplingId { get; init; }
    public int ParameterId { get; init; } 
    public required string ParameterResult { get; init; } 
    public required string SampleResult { get; init; } 
    public bool IsExtra { get; init; } 
    public long? MissionId { get; init; } 

    // Navigation properties
    public virtual required Mission Mission { get; init; }
    public virtual required ParameterService ParameterService { get; init; }
}