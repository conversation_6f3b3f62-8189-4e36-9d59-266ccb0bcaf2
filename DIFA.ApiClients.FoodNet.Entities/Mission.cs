namespace DIFA.ApiClients.FoodNet.Entities;

public class Mission
{
    public long MissionId { get; init; }
    public string MissionNb { get; init; }
    public DateTime? PlannedStartDateHour { get; init; }
    public DateTime? PlannedEndDateHour { get; init; }
    public DateTime? ActualStartDateHour { get; init; }
    public DateTime? ActualEndDateHour { get; init; }
    public DateTime? ClosingDate { get; init; }
    public long? OperatorId { get; init; }
    public int Noe { get; init; }
    public long? PostalCodeIdAdresControle { get; init; }
    public string PceCodeOperator { get; init; }
    public string PceCodeAddressControle { get; init; }
    public int Secret { get; init; }

    // Navigation properties
    public virtual ICollection<Sample> Samples { get; init; } = new List<Sample>();
}