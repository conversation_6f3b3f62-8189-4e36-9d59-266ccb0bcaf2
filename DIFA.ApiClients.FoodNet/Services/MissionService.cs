using AutoMapper;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IMissionService
{
    Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId, CancellationToken cancellationToken = default);
    Task<MissionDto?> GetMissionByIdAsync(string missionId, CancellationToken cancellationToken = default);

    Task<byte[]> GenerateMissionPdfAsync(string missionId, int templateVersionId,
        CancellationToken cancellationToken = default);
}

public class MissionService(FoodNetContext foodNetContext, AlphaContext alphaContext, IMapper mapper) : IMissionService
{
    public async Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId,
        CancellationToken cancellationToken = default)
    {
        var missions = await foodNetContext.Missions
            .Where(m => m.OperatorId == operatorId && m.Noe == 0)
            .OrderByDescending(m => m.ActualStartDateHour)
            .Select(m => new MissionDto
            {
                MissionId = m.MissionId,
                MissionNb = m.MissionNb,
                PlannedStartDate = m.PlannedStartDateHour,
                PlannedEndDate = m.PlannedEndDateHour,
                ActualStartDate = m.ActualStartDateHour,
                ActualEndDate = m.ActualEndDateHour,
                LCE = m.PceCodeOperator
            })
            .ToListAsync(cancellationToken);

        // Get LCE details from the Alpha context
        var lceCodes = missions.Where(m => !string.IsNullOrEmpty(m.LCE))
            .Select(m => m.LCE).Distinct().ToList();

        if (lceCodes.Any())
        {
            var lces = await alphaContext.LCEs
                .Where(lce => lceCodes.Contains(lce.ServiceCode))
                .ToListAsync(cancellationToken);

            // Map LCE data
            foreach (var mission in missions)
            {
                if (!string.IsNullOrEmpty(mission.LCE))
                {
                    var lce = lces.FirstOrDefault(lce => lce.ServiceCode == mission.LCE);
                    if (lce != null)
                        mission.LceDto = mapper.Map<LCEDto>(lce);
                }
            }
        }

        return missions;
    }

    public async Task<MissionDto?> GetMissionByIdAsync(string missionId, CancellationToken cancellationToken = default)
    {
        if (!long.TryParse(missionId, out var missionIdLong))
            return null;

        var mission = await foodNetContext.Missions
            .Where(m => m.MissionId == missionIdLong && m.Noe == 0)
            .Select(m => new MissionDto
            {
                MissionId = m.MissionId,
                MissionNb = m.MissionNb,
                PlannedStartDate = m.PlannedStartDateHour,
                PlannedEndDate = m.PlannedEndDateHour,
                ActualStartDate = m.ActualStartDateHour,
                ActualEndDate = m.ActualEndDateHour,
                LCE = m.PceCodeOperator
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (mission == null)
            return null;

        // Get LCE details if available
        if (!string.IsNullOrEmpty(mission.LCE))
        {
            var lce = await alphaContext.LCEs
                .FirstOrDefaultAsync(lce => lce.ServiceCode == mission.LCE, cancellationToken);

            if (lce != null)
                mission.LceDto = mapper.Map<LCEDto>(lce);
        }

        return mission;
    }

    public Task<byte[]> GenerateMissionPdfAsync(string missionId, int templateVersionId,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}