using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ICheckListService
{
    Task<List<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken);

    Task<List<QuestionWithAnswersDTO>> GetQuestionsWithAnswersAsync(int templateVersionId, string missionId,
        CancellationToken cancellationToken);
}

public class CheckListService : ICheckListService
{
    private readonly DynamoContext _dynamoContext;
    private readonly FoodNetContext _foodNetContext;

    public CheckListService(DynamoContext dynamoContext, FoodNetContext foodNetContext)
    {
        _dynamoContext = dynamoContext;
        _foodNetContext = foodNetContext;
    }

    public async Task<List<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken)
    {
        var sql = @"
            select i.Id              as item_id,
                   hso.TitelNL       as hoofdstuk_ouder_titel,
                   hs.TitelNL        as hoofdstuk_titel,
                   i.TitelNL         as titel,
                   ta.OmschrijvingNL as type_vraag_omschrijving,
                   ta.Id             as type_antwoord_id,
                   tv.Id             as template_version_id
            from dbo.TemplateVersie tv
                     inner join ItemTemplate it on it.TemplateVersie_Id = tv.Id
                     inner join DynamoTeste.dbo.ItemChecklistMoeder icm on icm.Id = it.ItemChecklistMoeder_Id
                     inner join DynamoTeste.dbo.Item i on icm.Item_Id = i.Id
                     inner join DynamoTeste.dbo.hoofdstuk hs on hs.Id = icm.Hoofdstuk_Id
                     left join DynamoTeste.dbo.hoofdstuk hso on hso.Id = hs.Ouder_Id
                     inner join TypeAntwoord ta on i.Antwoord_Id = ta.Id
            where tv.id = {0}";

        var questions = await _dynamoContext.Questions
            .FromSqlRaw(sql, templateVersionId)
            .ToListAsync(cancellationToken);

        // Apply ordering in memory since ORDER BY is not allowed in subqueries
        var orderedQuestions = questions
            .OrderBy(q => string.IsNullOrEmpty(q.ParentChapterTitle) ? 9999 : 0)
            .ThenBy(q => q.ParentTitle ?? "")
            .ThenBy(q => q.ItemId)
            .Select(q => new QuestionDto
            {
                ItemId = q.ItemId,
                ParentChapterTitle = q.ParentChapterTitle ?? "",
                ParentTitle = q.ParentTitle ?? "",
                Title = q.Title ?? "",
                QuestionType = q.QuestionType ?? "",
                QuestionTypeId = q.QuestionTypeId,
                TemplateVersionId = q.TemplateVersionId
            })
            .ToList();

        return orderedQuestions;
    }

    public async Task<List<QuestionWithAnswersDTO>> GetQuestionsWithAnswersAsync(int templateVersionId,
        string missionId, CancellationToken cancellationToken)
    {
        // Get questions from SQL Server (DynamoContext) using raw SQL
        var sql = @"
            select i.Id              as item_id,
                   hso.TitelNL       as hoofdstuk_ouder_titel,
                   hs.TitelNL        as hoofdstuk_titel,
                   i.TitelNL         as titel,
                   ta.OmschrijvingNL as type_vraag_omschrijving,
                   ta.Id             as type_antwoord_id,
                   tv.Id             as template_version_id
            from dbo.TemplateVersie tv
                     inner join ItemTemplate it on it.TemplateVersie_Id = tv.Id
                     inner join DynamoTeste.dbo.ItemChecklistMoeder icm on icm.Id = it.ItemChecklistMoeder_Id
                     inner join DynamoTeste.dbo.Item i on icm.Item_Id = i.Id
                     inner join DynamoTeste.dbo.hoofdstuk hs on hs.Id = icm.Hoofdstuk_Id
                     left join DynamoTeste.dbo.hoofdstuk hso on hso.Id = hs.Ouder_Id
                     inner join TypeAntwoord ta on i.Antwoord_Id = ta.Id
            where tv.id = {0}";

        var questions = await _dynamoContext.Questions
            .FromSqlRaw(sql, templateVersionId)
            .ToListAsync(cancellationToken);

        // Apply ordering in memory since ORDER BY is not allowed in subqueries
        questions = questions
            .OrderBy(q => string.IsNullOrEmpty(q.ParentChapterTitle) ? 9999 : 0)
            .ThenBy(q => q.ParentTitle ?? "")
            .ThenBy(q => q.ItemId)
            .ToList();

        // For integration testing, just return questions without answers
        // In a real implementation, this would get answers from Oracle using the complex Results.sql query
        var questionsWithAnswers = questions.Select(question => new QuestionWithAnswersDTO
        {
            ItemId = question.ItemId,
            ParentChapterTitle = question.ParentChapterTitle ?? "",
            ParentTitle = question.ParentTitle ?? "",
            Title = question.Title ?? "",
            QuestionType = question.QuestionType ?? "",
            QuestionTypeId = question.QuestionTypeId,
            Answer = null // No answers for integration testing
        }).ToList();

        return questionsWithAnswers;
    }
}