using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IMatrixService
{
    Task<MatrixDto?> GetMatrixByIdAsync(string id, CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatrixLevel4Async(CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatrixLevel5Async(CancellationToken cancellationToken);
    Task<List<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken);
}

public class MatrixService(AlphaContext alphaContext) : IMatrixService
{
    public async Task<MatrixDto?> GetMatrixByIdAsync(string id, CancellationToken cancellationToken)
    {
        // Try Level 4 first
        var level4Matrices = await GetMatrixLevel4Async(cancellationToken);
        var matrix = level4Matrices.FirstOrDefault(m => m.MatrixId == id);

        if (matrix != null)
            return matrix;

        // Try Level 5 if not found in Level 4
        var level5Matrices = await GetMatrixLevel5Async(cancellationToken);
        matrix = level5Matrices.FirstOrDefault(m => m.MatrixId == id);

        return matrix;
    }
    
    public async Task<List<MatrixDto>> GetMatrixLevel4Async(CancellationToken cancellationToken)
    {
        var matrices = await alphaContext.MatrixLevel4s
            .Select(m => new MatrixDto
            {
                MatrixId = m.MatrixNiv4Id,
                DescriptionNL = m.DescriptionNl,
                DescriptionFR = m.DescriptionFr,
                Level = 4
            })
            .ToListAsync(cancellationToken);

        return matrices;
    }

    public async Task<List<MatrixDto>> GetMatrixLevel5Async(CancellationToken cancellationToken)
    {
        var matrices = await alphaContext.MatrixLevel5s
            .Select(m => new MatrixDto
            {
                MatrixId = m.MatrixNiv5Id,
                DescriptionNL = m.DescriptionNl,
                DescriptionFR = m.DescriptionFr,
                Level = 5
            })
            .ToListAsync(cancellationToken);

        return matrices;
    }

    public async Task<List<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken)
    {
        return level switch
        {
            4 => await GetMatrixLevel4Async(cancellationToken),
            5 => await GetMatrixLevel5Async(cancellationToken),
            _ => new List<MatrixDto>()
        };
    }
}