using DIFA.ApiClients.FoodNet.Comparers;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ILCEService
{
    Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken);
}

public class LceService(AlphaContext alphaContext) : ILCEService
{
    public async Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken)
    {
        // Query LCE and PCE data using Entity Framework with query filters
        // The query filters in the context configuration automatically exclude NULL ServiceCode values

        var lceResults = await alphaContext.LCEs
            .Select(lce => new LCEDto
            {
                ServiceCode = lce.ServiceCode,
                DescriptionNl = lce.DescriptionNl,
                DescriptionFr = lce.DescriptionFr
            })
            .ToListAsync(cancellationToken);

        var pceResults = await alphaContext.PCEs
            .Select(pce => new LCEDto
            {
                ServiceCode = pce.ServiceCode,
                DescriptionNl = pce.DescriptionNl,
                DescriptionFr = pce.DescriptionFr
            })
            .ToListAsync(cancellationToken);

        // Combine and remove duplicates
        var combinedResults = lceResults
            .Union(pceResults, new LCEDtoComparer())
            .ToList();

        return combinedResults;
    }
}