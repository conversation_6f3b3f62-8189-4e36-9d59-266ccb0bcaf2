using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface IParameterService
{
    Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken);
}

public class Parameter(AlphaContext alphaContext) : IParameterService
{
    public async Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken)
    {
        var parameters = await alphaContext.Parameters
            .Select(p => new ParameterDto
            {
                Id = p.Id,
                DescriptionNL = p.DescriptionNl,
                DescriptionFR = p.DescriptionFr
            })
            .ToListAsync(cancellationToken);

        return parameters;
    }
}