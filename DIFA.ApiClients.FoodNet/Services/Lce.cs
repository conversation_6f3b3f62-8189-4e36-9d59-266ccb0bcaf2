using DIFA.ApiClients.FoodNet.Comparers;
using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ILCEService
{
    Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken);
}

public class Lce(AlphaContext alphaContext) : ILCEService
{
    public async Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken)
    {
        var lceResults = await alphaContext.LCEs
            .Where(lce => lce.ServiceCode != null)
            .Select(lce => new LCEDto
            {
                ServiceCode = lce.ServiceCode,
                DescriptionNl = lce.DescriptionNl,
                DescriptionFr = lce.DescriptionFr
            })
            .ToListAsync(cancellationToken);
        
        var pceResults = await alphaContext.PCEs
            .Where(pce => pce.ServiceCode != null)
            .Select(pce => new LCEDto
            {
                ServiceCode = pce.ServiceCode,
                DescriptionNl = pce.DescriptionNl,
                DescriptionFr = pce.DescriptionFr
            })
            .ToListAsync(cancellationToken);
        
        var combinedResults = lceResults
            .Union(pceResults, new LCEDtoComparer())
            .ToList();

        return combinedResults;
    }
}