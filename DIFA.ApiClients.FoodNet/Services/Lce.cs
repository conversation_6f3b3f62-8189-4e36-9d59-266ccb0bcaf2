using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ILCEService
{
    Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken);
}

public class Lce(AlphaContext alphaContext) : ILCEService
{
    public async Task<List<LCEDto>> GetLCEs(CancellationToken cancellationToken)
    {
        // Query LCE and PCE separately using anonymous types to avoid entity materialization issues
        var lceQuery = alphaContext.LCEs
            .Where(lce => !string.IsNullOrEmpty(lce.ServiceCode))
            .Select(lce => new { lce.ServiceCode, lce.DescriptionNl, lce.DescriptionFr });

        var pceQuery = alphaContext.PCEs
            .Where(pce => !string.IsNullOrEmpty(pce.ServiceCode))
            .Select(pce => new { pce.ServiceCode, pce.DescriptionNl, pce.DescriptionFr });

        // Execute queries and get the data
        var lceData = await lceQuery.ToListAsync(cancellationToken);
        var pceData = await pceQuery.ToListAsync(cancellationToken);

        // Combine and convert to DTOs
        var allData = lceData.Select(x => new LCEDto
        {
            ServiceCode = x.ServiceCode,
            DescriptionNl = x.DescriptionNl,
            DescriptionFr = x.DescriptionFr
        }).Union(pceData.Select(x => new LCEDto
        {
            ServiceCode = x.ServiceCode,
            DescriptionNl = x.DescriptionNl,
            DescriptionFr = x.DescriptionFr
        })).ToList();

        // Remove duplicates based on ServiceCode
        var uniqueResults = allData
            .GroupBy(x => x.ServiceCode)
            .Select(g => g.First())
            .ToList();

        return uniqueResults;
    }
}