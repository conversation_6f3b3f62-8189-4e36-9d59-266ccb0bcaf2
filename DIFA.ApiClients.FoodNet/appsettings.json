{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"FoodnetContext": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=hypnos.linux.ici)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=FOODTE1)));User Id=foodnet_consult;Password=*************;", "AlphaContext": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=hypnos.linux.ici)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=FOODTE1)));User Id=alpha_consult;Password=alpha_consult;", "DynamoContext": "Server=VM-MSSQLTESTE.teste.favv-afsca.be\\MSSQLTESTE;Database=DynamoTeste;Encrypt=false;User Id=Dynamo_Consult;Password=*************;"}}