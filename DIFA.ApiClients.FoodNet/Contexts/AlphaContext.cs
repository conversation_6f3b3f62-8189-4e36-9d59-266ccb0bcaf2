using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Contexts;

public class AlphaContext : DbContext
{
    public AlphaContext() { }

    public AlphaContext(DbContextOptions<AlphaContext> options) : base(options) { }

    public DbSet<LCE> LCEs { get; init; }
    public DbSet<PCE> PCEs { get; init; }
    public DbSet<ParameterService> Parameters { get; init; }
    public DbSet<MatrixLevel4> MatrixLevel4s { get; init; }
    public DbSet<MatrixLevel5> MatrixLevel5s { get; init; }
    public DbSet<LCEView> LCEViews { get; init; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<LCE>(entity =>
        {
            entity.HasKey(e => e.ServiceCode);
            entity.ToTable("LCE", "ALPHA");

            entity.Property(e => e.ServiceCode).HasColumnName("DIENST_CODE");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");

            // Add a query filter to exclude rows with NULL ServiceCode
            entity.HasQueryFilter(lce => lce.ServiceCode != null);
        });
        
        modelBuilder.Entity<PCE>(entity =>
        {
            entity.HasKey(e => e.ServiceCode);
            entity.ToTable("PCE", "ALPHA");

            entity.Property(e => e.ServiceCode).HasColumnName("DIENST_CODE");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");

            // Add a query filter to exclude rows with NULL ServiceCode
            entity.HasQueryFilter(pce => pce.ServiceCode != null);
        });
        

        modelBuilder.Entity<ParameterService>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToTable("PARAMETER", "ALPHA");

            entity.Property(e => e.Id).HasColumnName("PARAMETER_ID");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");
        });
        
        modelBuilder.Entity<MatrixLevel4>(entity =>
        {
            entity.HasKey(e => e.MatrixNiv4Id);
            entity.ToTable("MATRIX_NIV_4", "ALPHA");

            entity.Property(e => e.MatrixNiv4Id).HasColumnName("MATRIX_NIV_4_ID");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");
        });
        
        modelBuilder.Entity<MatrixLevel5>(entity =>
        {
            entity.HasKey(e => e.MatrixNiv5Id);
            entity.ToTable("MATRIX_NIV_5", "ALPHA");

            entity.Property(e => e.MatrixNiv5Id).HasColumnName("MATRIX_NIV_5_ID");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");
        });
        
        modelBuilder.Entity<LCEView>(entity =>
        {
            entity.HasNoKey(); // This is a view/query result
            entity.ToView("LCE_UNION_VIEW");

            entity.Property(e => e.ServiceCode).HasColumnName("DIENST_CODE");
            entity.Property(e => e.DescriptionNl).HasColumnName("OMSCHRIJVING_NL");
            entity.Property(e => e.DescriptionFr).HasColumnName("OMSCHRIJVING_FR");
        });
    }
}