using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Contexts;

public class DynamoContext : DbContext
{
    public DynamoContext() { }

    public DynamoContext(DbContextOptions<DynamoContext> options) : base(options) { }

    public DbSet<Question> Questions { get; init; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Question configuration - Based on Questions.sql
        modelBuilder.Entity<Question>(entity =>
        {
            entity.HasNoKey(); // This is a query result, not a table

            entity.Property(e => e.ItemId).HasColumnName("item_id");
            entity.Property(e => e.ParentChapterTitle).HasColumnName("hoofdstuk_ouder_titel");
            entity.Property(e => e.ParentTitle).HasColumnName("hoofdstuk_titel");
            entity.Property(e => e.Title).HasColumnName("titel");
            entity.Property(e => e.QuestionType).HasColumnName("type_vraag_omschrijving");
            entity.Property(e => e.QuestionTypeId).HasColumnName("type_antwoord_id");
            entity.Property(e => e.TemplateVersionId).HasColumnName("template_version_id");
        });
    }
}