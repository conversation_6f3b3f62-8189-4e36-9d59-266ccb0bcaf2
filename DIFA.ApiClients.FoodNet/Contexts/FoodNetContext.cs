using DIFA.ApiClients.FoodNet.Entities;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Contexts;

public class FoodNetContext : DbContext
{
    public FoodNetContext() { }

    public FoodNetContext(DbContextOptions<FoodNetContext> options) : base(options) { }

    public DbSet<SampleService> Samples { get; init; }
    public DbSet<SampleList> SampleLists { get; init; }
    public DbSet<Mission> Missions { get; init; }
    public DbSet<Answer> Answers { get; init; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Sample configuration - Based on test.sql structure
        modelBuilder.Entity<SampleService>(entity =>
        {
            entity.HasKey(e => e.SampleId);
            entity.ToTable("MONSTERNAME_PARAMETER", "FOODNET"); // FOODNET schema

            entity.Property(e => e.SampleId).HasColumnName("MONSTERNAME_PARAMETER_ID");
            entity.Property(e => e.SampleParameterId).HasColumnName("MONSTERNAME_PARAMETER_ID_OUDER");
            entity.Property(e => e.SampleNb).HasColumnName("MONSTERNUMMER");
            entity.Property(e => e.ControlId).HasColumnName("CONTROLE_ID");
            entity.Property(e => e.SamplingId).HasColumnName("MONSTERNAME_ID");
            entity.Property(e => e.ParameterId).HasColumnName("PARAMETER_ID_ALPHA");
            entity.Property(e => e.ParameterResult).HasColumnName("PARAMETER_RESULT");
            entity.Property(e => e.SampleResult).HasColumnName("RESULTAAT");
            entity.Property(e => e.IsExtra).HasColumnName("IS_EXTRA");
            entity.Property(e => e.MissionId).HasColumnName("MISSIE_ID");

            entity.HasOne(s => s.Mission)
                .WithMany()
                .HasForeignKey(s => s.MissionId);

            entity.HasOne(s => s.ParameterService)
                .WithMany(p => p.Samples)
                .HasForeignKey(s => s.ParameterId);
        });

        // SampleList configuration - Based on sample list queries
        modelBuilder.Entity<SampleList>(entity =>
        {
            entity.HasNoKey(); // This is a view/query result
            entity.ToView("SAMPLE_LIST_VIEW"); // Use ToView for query-based entities

            entity.Property(e => e.SampleNumber).HasColumnName("MONSTERNUMMER");
            entity.Property(e => e.ControlId).HasColumnName("CONTROLE_ID");
            entity.Property(e => e.SampleDate).HasColumnName("DATUMMONSTERNAME");
            entity.Property(e => e.Result).HasColumnName("RESULTAAT");
            entity.Property(e => e.MatrixLevel4Id).HasColumnName("MATRIX_NIV_4_ID");
            entity.Property(e => e.MatrixLevel5Id).HasColumnName("MATRIX_NIV_5_ID");
            entity.Property(e => e.OperatorId).HasColumnName("OPERATOR_ID");
        });

        // Mission configuration - Based on Mission.sql and MissionList.sql
        modelBuilder.Entity<Mission>(entity =>
        {
            entity.HasKey(e => e.MissionId);
            entity.ToTable("MISSIE", "FOODNET"); // FOODNET.MISSIE table

            entity.Property(e => e.MissionId).HasColumnName("MISSIE_ID");
            entity.Property(e => e.MissionNb).HasColumnName("MISSIENUMMER");
            entity.Property(e => e.PlannedStartDateHour).HasColumnName("GEPLANDEBEGINDATUMUUR");
            entity.Property(e => e.PlannedEndDateHour).HasColumnName("GEPLANDEEINDDATUMUUR");
            entity.Property(e => e.ActualStartDateHour).HasColumnName("WERKELIJKEBEGINDATUMUUR");
            entity.Property(e => e.ActualEndDateHour).HasColumnName("WERKELIJKEEINDDATUMUUR");
            entity.Property(e => e.OperatorId).HasColumnName("OPERATOR_ID");
            entity.Property(e => e.PceCodeOperator).HasColumnName("PCE_CONTROLEUR");
            entity.Property(e => e.Noe).HasColumnName("NOE");
        });

        // Answer configuration - Simple configuration for integration testing
        modelBuilder.Entity<Answer>(entity =>
        {
            entity.HasNoKey(); // This is for integration testing only
            entity.ToTable("VRAAGRESULTAAT", "FOODNET");

            // Only map the basic properties that exist in the table
            entity.Property(e => e.Id).HasColumnName("vraagresultaat_id");
            entity.Property(e => e.Result).HasColumnName("RESULTAAT");
            entity.Property(e => e.ScoreId).HasColumnName("score_id");
            // Don't map MissionId since it doesn't exist in this table
        });
    }
}