using DIFA.ApiClients.FoodNet.Contract;

namespace DIFA.ApiClients.FoodNet.Comparers;

/// <summary>
/// Comparer for LCEDto to handle UNION operation
/// </summary>
public class LCEDtoComparer : IEqualityComparer<LCEDto>
{
    public bool Equals(LCEDto x, LCEDto y)
    {
        if (x == null && y == null) return true;
        if (x == null || y == null) return false;
        return x.ServiceCode == y.ServiceCode;
    }

    public int GetHashCode(LCEDto obj)
    {
        return obj?.ServiceCode?.GetHashCode() ?? 0;
    }
}