using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Services;

namespace DIFA.ApiClients.FoodNet;

public class FoodNetClient(
    ILCEService ilceService,
    IMatrixService matrixService,
    IParameterService parameterService,
    ICheckListService checkListService,
    IMissionService missionService,
    ISampleService sampleService,
    IAnswerService answerService)
    : IFoodNetClient
{   
    #region Mission Operations
    public async Task<IEnumerable<MissionDto>> GetMissionsAsync(int operatorId, CancellationToken cancellationToken = default)
    {
        return await missionService.GetMissionsAsync(operatorId, cancellationToken);
    }

    public async Task<MissionDto> GetMissionByIdAsync(string missionId, CancellationToken cancellationToken = default)
    {
        return await missionService.GetMissionByIdAsync(missionId, cancellationToken);
    }

    public async Task<byte[]> GenerateMissionPdfAsync(string missionId, int templateVersionId, CancellationToken cancellationToken = default)
    {
        return await missionService.GenerateMissionPdfAsync(missionId, templateVersionId, cancellationToken);
    }
    #endregion

    #region Sample Operations
    
    public async Task<IEnumerable<SampleListDto>> GetSamplesAsync(int operatorId, CancellationToken cancellationToken = default)
    {
        return await sampleService.GetSamplesAsync(operatorId, cancellationToken);
    }

    public async Task<IEnumerable<SampleDto>> GetSampleDetailsAsync(int operatorId, int controlId, CancellationToken cancellationToken = default)
    {
        return await sampleService.GetSampleDetailsAsync(operatorId, controlId, cancellationToken);
    }

    public async Task<SampleDto> GetSampleByIdAsync(string sampleId, CancellationToken cancellationToken = default)
    {
        return await sampleService.GetSampleByIdAsync(sampleId, cancellationToken);
    }
    
    #endregion

    #region LCE Operations
    public async Task<IEnumerable<LCEDto>> GetLCEsAsync(CancellationToken cancellationToken = default)
    {
        return await ilceService.GetLCEs(cancellationToken);
    }

    public async Task<LCEDto> GetLCEByServiceCodeAsync(string serviceCode, CancellationToken cancellationToken = default)
    {
        var lces = await ilceService.GetLCEs(cancellationToken);
        return lces.FirstOrDefault(lce => lce.ServiceCode == serviceCode);
    }
    #endregion

    #region Matrix Operations
    public async Task<IEnumerable<MatrixDto>> GetMatricesLevel4Async(CancellationToken cancellationToken = default)
    {
        return await matrixService.GetMatrixLevel4Async(cancellationToken);
    }

    public async Task<IEnumerable<MatrixDto>> GetMatricesLevel5Async(CancellationToken cancellationToken = default)
    {
        return await matrixService.GetMatrixLevel5Async(cancellationToken);
    }

    public async Task<IEnumerable<MatrixDto>> GetMatricesByLevelAsync(int level, CancellationToken cancellationToken = default)
    {
        return await matrixService.GetMatricesByLevelAsync(level, cancellationToken);
    }

    public async Task<MatrixDto> GetMatrixByIdAsync(string matrixId, CancellationToken cancellationToken = default)
    {
        return await matrixService.GetMatrixByIdAsync(matrixId, cancellationToken);
    }
    #endregion

    #region Parameter Operations
    public async Task<IEnumerable<ParameterDto>> GetParametersAsync(CancellationToken cancellationToken = default)
    {
        return await parameterService.GetParametersAsync(cancellationToken);
    }

    public async Task<ParameterDto> GetParameterByIdAsync(int parameterId, CancellationToken cancellationToken = default)
    {
        var parameters = await parameterService.GetParametersAsync(cancellationToken);
        return parameters.FirstOrDefault(p => p.Id == parameterId);
    }
    #endregion

    #region Checklist Operations
    public async Task<IEnumerable<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken = default)
    {
        return await checkListService.GetQuestionsAsync(templateVersionId, cancellationToken);
    }

    public async Task<IEnumerable<AnswerDto>> GetAnswersAsync(string missionId, CancellationToken cancellationToken = default)
    {
        return await answerService.GetAnswersAsync(missionId, cancellationToken);
    }

    public async Task<IEnumerable<AnswerDto>> GetAnswersByTemplateAsync(int templateVersionId, CancellationToken cancellationToken = default)
    {
        return await answerService.GetAnswersByTemplateAsync(templateVersionId, cancellationToken);
    }

    public async Task<AnswerDto> CreateAnswerAsync(AnswerDto AnswerDto, CancellationToken cancellationToken = default)
    {
        return await answerService.CreateAnswerAsync(AnswerDto, cancellationToken);
    }

    public async Task<AnswerDto> UpdateAnswerAsync(int id, AnswerDto AnswerDto, CancellationToken cancellationToken = default)
    {
        return await answerService.UpdateAnswerAsync(id, AnswerDto, cancellationToken);
    }

    public async Task DeleteAnswerAsync(int id, CancellationToken cancellationToken = default)
    {
        await answerService.DeleteAnswerAsync(id, cancellationToken);
    }

    #endregion
}