namespace DIFA.ApiClients.FoodNet.Contract;

public class MissionDto
{
    public long MissionId { get; init; } // Changed to long to match entity

    public string MissionNb { get; init; }

    public DateTime? PlannedStartDate { get; init; } // Made nullable to match entity

    public DateTime? PlannedEndDate { get; init; } // Made nullable to match entity

    public DateTime? ActualStartDate { get; init; } // Made nullable to match entity

    public DateTime? ActualEndDate { get; init; } // Made nullable to match entity

    public string? LCE { get; init; } // This will be populated from PCE_CONTROLEUR

    public LCEDto? LceDto { get; set; } // Made nullable
}