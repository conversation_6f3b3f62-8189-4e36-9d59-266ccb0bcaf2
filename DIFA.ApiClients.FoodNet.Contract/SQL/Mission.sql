SELECT m.MISSIE_ID             as mission_id,
       <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>            as mission_nb,
       GEPLANDEBEGINDATUMUUR   as planned_start_date,
       GEPLANDEEINDDATUMUUR    as planned_end_date,
       WER<PERSON><PERSON>IJKEBEGINDATUMUUR as actual_start_date,
       WER<PERSON><PERSON><PERSON>JKEEINDDATUMUUR  as actual_end_date
FROM FOODNET.MISSIE m
where m.NOE = 0
  and m.MISSIE_ID = :p0 --1673
  and m.OPERATOR_ID = :p1 -- 2087535
