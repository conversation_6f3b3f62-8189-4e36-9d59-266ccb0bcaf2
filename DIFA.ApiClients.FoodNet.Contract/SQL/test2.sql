SELECT
mn.CONTROLE_ID                                      AS controle_id,
m.<PERSON>ON<PERSON>ERNUMMER                                     AS monsternummer,
mp.MONSTERNAME_PARAMETER_ID_OUDER                   AS parentid,
mp.MONSTERNAME_PARAMETER_ID                         AS monstername_parameter_id,
mp.PARAMETER_ID_ALPHA                               AS parameterid,
'todo description via alpha'                        AS parametername,
-- XMLCAST(
--         XMLQUERY(
--                 'declare default element namespace ""; /language/nl/text()'
--                 PASSING ps.OMSCHRIJVING_MT
--                 RETURNING CONTENT
--         ) AS varchar2(100))                         AS parameter_result,
-- mp.IS_EXTRA                                         AS extra,
-- resultaat_eenheid_code,
-- COALESCE(XMLCAST(XMLQUERY(
--         'declare default element namespace ""; /language/nl/text()'
--         PASSING mrrc.OMSCHRIJVING_MT
--         RETURNING CONTENT
--                  ) AS varchar2(100)), mr.resultaat) AS resultaat,
-- mr.<PERSON><PERSON><PERSON>_UITVOERING_BEGIN,
-- mr.<PERSON><PERSON>UM_UITVOERING_EIND,
-- mr.<PERSON><PERSON><PERSON><PERSON>                                          AS ccalpha,
-- mr.<PERSON><PERSON>                                           AS ccbeta,
mp.ONDERGRENS_GEVALIDEERD                           AS ondergrens,
mp.BOVENGRENS_GEVALIDEERD                           AS bovengrens
-- XMLCAST(
--         XMLQUERY(
--                 'declare default element namespace ""; /language/nl/text()'
--                 PASSING mr.attributen
--                 RETURNING CONTENT
--         ) AS varchar2(100)
-- )                                                   AS attributen,
FROM FOODNET.MONSTERNAME_PARAMETER mp
         INNER JOIN FOODNET.MONSTER m
                    ON m.MONSTERNAME_ID = mp.MONSTERNAME_ID
         INNER JOIN FOODNET.MONSTERNAME mn
                    ON m.MONSTERNAME_ID = mn.MONSTERNAME_ID
--          INNER JOIN FOODNET.MONSTER_RESULTAAT mr
--                     ON mp.MONSTERNAME_PARAMETER_ID = mr.MONSTERNAME_PARAMETER_ID
--          INNER JOIN FOODNET.PARAMETER_SCORE ps
--                     ON ps.PARAMETER_SCORE_ID = mp.PARAMETER_SCORE_ID
--          INNER JOIN FOODNET.CONTROLE C2
--                     ON mn.CONTROLE_ID = C2.CONTROLE_ID
--          LEFT JOIN FOODNET.MONSTER_RESULT_RESULT_CODE mrrc
--                    ON mrrc.MONSTER_RESULT_RESULT_CODE_ID = mr.MONSTER_RESULT_RESULT_CODE_ID
WHERE mn.CONTROLE_ID = 2627504;

--m.MONSTERNUMMER = 2799190121 --

-- 1488190570 -- 3460210012 -- 3340150049 -- 2441190291
-- START WITH mp.MONSTERNAME_PARAMETER_ID_OUDER IS NULL
-- CONNECT BY PRIOR mp.MONSTERNAME_PARAMETER_ID = mp.MONSTERNAME_PARAMETER_ID_OUDER
-- ORDER SIBLINGS BY mp.MONSTERNAME_PARAMETER_ID;
