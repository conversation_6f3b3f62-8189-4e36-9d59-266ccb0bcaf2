SELECT
    mp.MONSTERNAME_PARAMETER_ID_OUDER,
    mp.MONSTERNAME_PARAMETER_ID,
    mp.PARAMETER_ID_ALPHA AS parameterid,
    'todo description via alpha' AS parametername,
    xmlcast(
            XMLQUERY(
                    'declare default element namespace ""; /language/nl/text()'
                        PASSING ps.OMSCHRIJVING_MT
    RETURNING CONTENT
            ) AS varchar2(100)) AS parameter_result,
    mp.IS_EXTRA AS extra,
    resultaat_eenheid_code,
    COALESCE(XMLCAST(XMLQUERY(
            'declare default element namespace ""; /language/nl/text()'
                PASSING mrrc.OMSCHRIJVING_MT
    RETURNING CONTENT
                     ) AS varchar2(100)),resultaat) as resultaat,
    mr.DATUM_UITVOERING_BEGIN,
    mr.DATUM_UITVOERING_EIND,
    mr.<PERSON><PERSON><PERSON>HA AS ccalpha,
    mr.<PERSON><PERSON><PERSON> AS ccbeta,
    mp.ONDERGRENS_GEVALIDEERD AS ondergrens,
    mp.BOVENGRENS_GEVALIDEERD  AS bovengrens,
    xmlcast(
            XMLQUERY(
                    'declare default element namespace ""; /language/nl/text()'
                        PASSING mr.attributen
	    	RETURNING CONTENT
            ) AS varchar2(100)
    ) AS attributen
FROM FOODNET.MONSTER m
         INNER JOIN
     FOODNET.MONSTERNAME mn ON m.MONSTERNAME_ID = mn.MONSTERNAME_ID
         INNER JOIN
     FOODNET.MONSTERNAME_PARAMETER mp ON mp.MONSTERNAME_ID = mn.MONSTERNAME_ID
         INNER JOIN
     FOODNET.MONSTER_RESULTAAT mr ON mp.MONSTERNAME_PARAMETER_ID  = mr.MONSTERNAME_PARAMETER_ID
         INNER JOIN
     FOODNET.PARAMETER_SCORE ps ON ps.PARAMETER_SCORE_ID = mp.PARAMETER_SCORE_ID
         LEFT JOIN
     FOODNET.MONSTER_RESULT_RESULT_CODE mrrc ON mrrc.MONSTER_RESULT_RESULT_CODE_ID = mr.MONSTER_RESULT_RESULT_CODE_ID
WHERE
    mn.CONTROLE_ID = 2627504
 --   m.MONSTERNUMMER = 1488190570 -- 1488190570 -- 3460210012 -- 3340150049
;





