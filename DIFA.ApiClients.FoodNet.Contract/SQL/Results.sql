SELECT m.MISSIE_ID as missionId, 
       score.OMSCHRIJVING_MT , 
       v.RESULTAAT as resultaat, 
       v.score_id as scoreId,
       t.OMSCHRIJVING_MT, 
       s.OMSCHRIJVING_MT,
       c.TEMPLATEVERSIE_ID, v.vraagresultaat_id        
FROM
    foodnet.MISSIE m
        INNER JOIN foodnet.CONTROLE c  ON c.MISSIE_ID = m.MISSIE_ID
        INNER JOIN foodnet.TEMPLATEVERSIERESULTAAT tvr ON tvr.controle_id = c.CONTROLE_ID
        INNER JOIN foodnet.VRAAGRESULTAAT v ON tvr.TEMPLATEVERSIERESULTAAT_ID = v.TEMPLATEVERSIERESULTAAT_ID
        LEFT JOIN foodnet.SCORE score ON v.SCORE_ID = score.SCORE_ID
        INNER JOIN foodnet.TYPECONTROLE t ON t.TYPECONTROLE_ID  = c.TYPECONTROLE_ID
        INNER JOIN foodnet.SOORTCONTROLE s ON s.SOORTCONTROLE_ID  = c.SOORTCONTROLE_ID
        INNER JOIN foodnet.SELECTIEVRAAGRESULTAAT svr ON svr.MISSIE_ID = m.MISSIE_ID
WHERE m.MISSIENUMMER  = '2462/21/083243'