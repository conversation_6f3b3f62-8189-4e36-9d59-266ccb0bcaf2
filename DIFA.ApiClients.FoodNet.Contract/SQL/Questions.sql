select i.Id              as item_id,
       hso.TitelNL       as hoofdstuk_ouder_titel,
       hs.TitelNL        as hoofdstuk_titel,
       i.TitelNL         as titel,
       ta.OmschrijvingNL as type_vraag_omschrijving,
       ta.Id             as type_antwoord_id
from dbo.TemplateVersie tv
         inner join ItemTemplate it on it.TemplateVersie_Id = tv.Id
         inner join DynamoTeste.dbo.ItemChecklistMoeder icm on icm.Id = it.ItemChecklistMoeder_Id
         inner join DynamoTeste.dbo.Item i on icm.Item_Id = i.Id
         inner join DynamoTeste.dbo.hoofdstuk hs on hs.Id = icm.Hoofdstuk_Id
         left join DynamoTeste.dbo.hoofdstuk hso on hso.Id = hs.Ouder_Id
         inner join TypeAntwoord ta on i.Antwoord_Id = ta.Id
where tv.id = 9363
order by isnull(hso.Volgorde, 9999),
         hs.Volgorde asc,
         icm.volgorde asc
;