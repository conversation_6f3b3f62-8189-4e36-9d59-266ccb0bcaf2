namespace DIFA.ApiClients.FoodNet.Contract;

public class SampleDto
{
    public string SampleId { get; init; }
    public string SampleParameterId { get; init; }
    public string SampleNb { get; init; }
    public string ControlId { get; init; }
    public string SamplingId { get; init; }
    public ParameterDto Parameter { get; set; }
    public int ParameterId { get; init; }
    public string ParameterResult { get; init; }
    public string SampleResult { get; init; }
    public bool IsExtra { get; init; }
    public long? MissionId { get; init; }
}