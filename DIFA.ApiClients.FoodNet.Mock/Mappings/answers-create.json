{"Guid": "243e38f8-9705-48f0-9e99-bbaeb6a496b3", "Title": "Create Answer", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/answers"}]}, "Methods": ["POST"], "Headers": [{"Name": "Content-Type", "Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "application/json*"}]}]}, "Response": {"StatusCode": 201, "Headers": {"Content-Type": "application/json", "Location": "/api/answers/5"}, "Body": {"id": 5, "templateVersionId": 9363, "questionResultId": 5, "result": "<PERSON><PERSON><PERSON>wo<PERSON>", "scoreId": 1, "missionId": "a7b8c9d0-e1f2-3456-abcd-************"}}}