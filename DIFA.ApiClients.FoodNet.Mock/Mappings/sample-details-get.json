{"Guid": "0c48ccb7-5b4d-487f-88c4-a38f1e58f937", "Title": "Get Sample Details", "Request": {"Path": {"Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "/api/samples/details"}]}, "Methods": ["GET"], "Params": [{"Name": "operatorId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "2087535"}]}, {"Name": "controlId", "Matchers": [{"Name": "ExactMatcher", "Pattern": "12345"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": "[{\"sampleId\":\"q7r8s9t0-u1v2-3456-qrst-************\",\"sampleParameterId\":\"r8s9t0u1-v2w3-4567-rstu-************\",\"sampleNb\":\"s9t0u1v2-w3x4-5678-stuv-************\",\"controlId\":\"12345\",\"samplingId\":\"t0u1v2w3-x4y5-6789-tuvw-012345678901\",\"parameterId\":1,\"parameterResult\":\"< 10 CFU/g\",\"sampleResult\":\"CONFORM\",\"isExtra\":false,\"missionId\":1673,\"parameter\":{\"id\":1,\"descriptionNL\":\"Salmonella spp.\",\"descriptionFR\":\"Salmonella spp.\"}},{\"sampleId\":\"u1v2w3x4-y5z6-7890-uvwx-123456789012\",\"sampleParameterId\":\"v2w3x4y5-z6a7-8901-vwxy-234567890123\",\"sampleNb\":\"w3x4y5z6-a7b8-9012-wxyz-345678901234\",\"controlId\":\"12345\",\"samplingId\":\"x4y5z6a7-b8c9-0123-xyza-456789012345\",\"parameterId\":2,\"parameterResult\":\"> 100 CFU/g\",\"sampleResult\":\"NON-CONFORM\",\"isExtra\":true,\"missionId\":1673,\"parameter\":{\"id\":2,\"descriptionNL\":\"Listeria monocytogenes\",\"descriptionFR\":\"Listeria monocytogenes\"}}]"}}