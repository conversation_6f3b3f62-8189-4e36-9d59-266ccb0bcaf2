{"Guid": "920444e9-e43b-42c1-a0ec-c1d2ebb9db1c", "Title": "Update Answer", "Request": {"Path": {"Matchers": [{"Name": "RegexMatcher", "Pattern": "^/api/answers/(\\d+)$"}]}, "Methods": ["PUT"], "Headers": [{"Name": "Content-Type", "Matchers": [{"Name": "Wildcard<PERSON><PERSON><PERSON>", "Pattern": "application/json*"}]}]}, "Response": {"StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Body": {"id": 1, "templateVersionId": 9363, "questionResultId": 1, "result": "Bijgewerkt antwoord", "scoreId": 2, "missionId": "a7b8c9d0-e1f2-3456-abcd-************"}}}